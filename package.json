{"name": "pickslot-email-service", "type": "module", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "test": "vitest", "test:coverage": "vitest --coverage", "setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh"}, "dependencies": {"hono": "^4.8.5"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250719.0", "@types/node": "^20.0.0", "@vitest/coverage-v8": "^1.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0", "wrangler": "^4.4.0"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad"}