import { BaseEmailProvider } from './base.js';
import { EmailData, EmailResponse } from '../types/email.js';

export class PostmarkProvider extends BaseEmailProvider {
  async send(emailData: EmailData): Promise<EmailResponse> {
    this.validateEmailData(emailData);

    const payload = {
      From: this.getFromEmail(emailData),
      To: this.normalizeEmailArray(emailData.to).join(','),
      Subject: emailData.subject,
      HtmlBody: emailData.html,
      TextBody: emailData.text,
      Cc: emailData.cc?.join(','),
      Bcc: emailData.bcc?.join(','),
      Attachments: emailData.attachments?.map(att => ({
        Name: att.filename,
        Content: att.content,
        ContentType: att.contentType || 'application/octet-stream'
      }))
    };

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'X-Postmark-Server-Token': this.config.config.apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Postmark API error: ${result.Message || response.statusText}`);
    }

    return {
      success: true,
      messageId: result.MessageID,
      provider: this.config.id
    };
  }
}