export interface EmailProviderConfig {
  id: string;
  type: 'resend' | 'brevo' | 'mailgun' | 'sendgrid' | 'postmark';
  priority: 1 | 2 | 3;
  weight?: number;
  enabled: boolean;
  config: {
    apiKey: string;
    domain?: string;
    fromEmail?: string;
    [key: string]: any;
  };
}

export interface EmailData {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
  cc?: string[];
  bcc?: string[];
  attachments?: Array<{
    filename: string;
    content: string;
    contentType?: string;
  }>;
}

export interface EmailResponse {
  success: boolean;
  messageId?: string;
  provider?: string;
  error?: string;
  failedProviders?: string[];
  retryCount?: number;
}

export interface ProviderStatus {
  id: string;
  enabled: boolean;
  healthy: boolean;
  lastFailure?: number;
  failureCount: number;
  nextRetryAt?: number;
}