import type { KVNamespace } from "@cloudflare/workers-types";

export interface CloudflareBindings {
  PICKSLOT_EMAIL_CACHE: KVNamespace;
  RESEND_API_KEY?: string;
  BREVO_API_KEY?: string;
  MAILGUN_API_KEY?: string;
  SENDGRID_API_KEY?: string;
  POSTMARK_API_KEY?: string;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
}

export interface LoadBalancerConfig {
  maxRetries: number;
  retryDelay: number;
  circuitBreaker: CircuitBreakerConfig;
}
