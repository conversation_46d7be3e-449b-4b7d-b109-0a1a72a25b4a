import { CacheManager } from '../utils/cache.js';
import { CircuitBreakerConfig } from '../types/config.js';

export class CircuitBreaker {
  constructor(
    private cache: CacheManager,
    private config: CircuitBreakerConfig
  ) {}

  async canExecute(providerId: string): Promise<boolean> {
    const status = await this.cache.getProviderStatus(providerId);
    
    if (!status || status.healthy) {
      return true;
    }

    // Check if we should retry
    if (status.nextRetryAt && Date.now() < status.nextRetryAt) {
      return false;
    }

    // Check if failure threshold exceeded
    if (status.failureCount >= this.config.failureThreshold) {
      const timeSinceLastFailure = Date.now() - (status.lastFailure || 0);
      if (timeSinceLastFailure < this.config.resetTimeout) {
        return false;
      }
    }

    return true;
  }

  async recordSuccess(providerId: string): Promise<void> {
    await this.cache.resetFailureCount(providerId);
  }

  async recordFailure(providerId: string): Promise<void> {
    const failureCount = await this.cache.incrementFailureCount(providerId);
    
    if (failureCount >= this.config.failureThreshold) {
      const status = await this.cache.getProviderStatus(providerId);
      if (status) {
        // Exponential backoff
        const backoffTime = Math.min(
          this.config.resetTimeout * Math.pow(2, failureCount - this.config.failureThreshold),
          this.config.resetTimeout * 8 // Max 8x the reset timeout
        );
        status.nextRetryAt = Date.now() + backoffTime;
        await this.cache.setProviderStatus(providerId, status);
      }
    }
  }
}