#!/bin/bash

echo "🎯 PickSlot Email Service - Complete Setup"
echo "========================================"

# Kiểm tra dependencies
echo "1. Kiểm tra dependencies..."
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm chưa được cài đặt. Vui lòng cài đặt pnpm trước."
    exit 1
fi

if ! command -v wrangler &> /dev/null; then
    echo "❌ wrangler chưa được cài đặt. Đang cài đặt..."
    pnpm add -g wrangler
fi

# Cài đặt packages
echo "2. Cài đặt packages..."
pnpm install

# Tạo KV namespace
echo "3. Tạo KV namespace..."
chmod +x scripts/create-kv.sh
./scripts/create-kv.sh

# Thiết lập secrets
echo "4. Thiết lập API keys..."
chmod +x scripts/setup-secrets.sh
./scripts/setup-secrets.sh

# Generate types
echo "5. Generate TypeScript types..."
pnpm run cf-typegen

# Tạo .dev.vars file
if [ ! -f ".dev.vars" ]; then
    echo "6. Tạo .dev.vars file..."
    cp .dev.vars.example .dev.vars
    echo "⚠️  Vui lòng cập nhật .dev.vars với API keys của bạn cho local development"
fi

# Chạy tests
echo "7. Chạy tests..."
pnpm test

echo ""
echo "✅ Setup hoàn tất!"
echo ""
echo "📋 Các bước tiếp theo:"
echo "  1. Cập nhật .dev.vars với API keys cho local development"
echo "  2. Chạy 'pnpm run dev' để test local"
echo "  3. Chạy './scripts/deploy.sh' để deploy lên production"
echo ""
echo "🔗 Useful commands:"
echo "  pnpm run dev          - Start local development"
echo "  pnpm run deploy       - Deploy to production"
echo "  pnpm test            - Run tests"
echo "  ./scripts/test-api.sh - Test deployed API"