import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { EmailService } from "./services/email.js";
import { CacheManager } from "./utils/cache.js";
import { EmailData, EmailProviderConfig } from "./types/email.js";
import { CloudflareBindings, LoadBalancerConfig } from "./types/config.js";

const app = new Hono<{ Bindings: CloudflareBindings }>();

// Middleware
app.use("*", cors());
app.use("*", logger());

// Default configuration
const defaultConfig: LoadBalancerConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  circuitBreaker: {
    failureThreshold: 5,
    resetTimeout: 60000,
    monitoringPeriod: 300000,
  },
};

// Helper to get providers from environment or cache
async function getProviders(c: any): Promise<EmailProviderConfig[]> {
  const cache = new CacheManager(c.env.PICKSLOT_EMAIL_CACHE);

  // Try to get from cache first
  let providers = await cache.getConfig("providers");

  if (!providers) {
    // Fallback to environment variables
    providers = [];

    if (c.env.RESEND_API_KEY) {
      providers.push({
        id: "resend-default",
        type: "resend",
        priority: 1,
        enabled: true,
        config: { apiKey: c.env.RESEND_API_KEY },
      });
    }

    if (c.env.BREVO_API_KEY) {
      providers.push({
        id: "brevo-default",
        type: "brevo",
        priority: 2,
        enabled: true,
        config: { apiKey: c.env.BREVO_API_KEY },
      });
    }

    if (c.env.MAILGUN_API_KEY) {
      providers.push({
        id: "mailgun-default",
        type: "mailgun",
        priority: 2,
        enabled: true,
        config: {
          apiKey: c.env.MAILGUN_API_KEY,
          domain: c.env.MAILGUN_DOMAIN || "example.com",
        },
      });
    }

    if (c.env.SENDGRID_API_KEY) {
      providers.push({
        id: "sendgrid-default",
        type: "sendgrid",
        priority: 3,
        enabled: true,
        config: { apiKey: c.env.SENDGRID_API_KEY },
      });
    }

    if (c.env.POSTMARK_API_KEY) {
      providers.push({
        id: "postmark-default",
        type: "postmark",
        priority: 3,
        enabled: true,
        config: { apiKey: c.env.POSTMARK_API_KEY },
      });
    }
  }

  return providers;
}

// Routes
app.get("/", (c) => {
  return c.json({
    message: "PickSlot Email Service",
    version: "1.0.0",
    endpoints: [
      "POST /send",
      "POST /send/bulk",
      "GET /providers/status",
      "POST /providers/test",
      "PUT /providers/config",
    ],
  });
});

app.post("/send", async (c) => {
  try {
    const emailData: EmailData = await c.req.json();
    const providers = await getProviders(c);

    if (providers.length === 0) {
      return c.json({ error: "No email providers configured" }, 400);
    }

    const cache = new CacheManager(c.env.PICKSLOT_EMAIL_CACHE);
    const emailService = new EmailService(cache, defaultConfig);

    const result = await emailService.send(emailData, providers);

    return c.json(result, result.success ? 200 : 500);
  } catch (error) {
    console.error("Send email error:", error);
    return c.json(
      {
        error: error instanceof Error ? error.message : "Unknown error",
      },
      500
    );
  }
});

app.post("/send/bulk", async (c) => {
  try {
    const { emails }: { emails: EmailData[] } = await c.req.json();
    const providers = await getProviders(c);

    if (providers.length === 0) {
      return c.json({ error: "No email providers configured" }, 400);
    }

    const cache = new CacheManager(c.env.PICKSLOT_EMAIL_CACHE);
    const emailService = new EmailService(cache, defaultConfig);

    const results = await emailService.sendBulk(emails, providers);

    return c.json({ results });
  } catch (error) {
    console.error("Bulk send error:", error);
    return c.json(
      {
        error: error instanceof Error ? error.message : "Unknown error",
      },
      500
    );
  }
});

app.get("/providers/status", async (c) => {
  try {
    console.log("xxx:", c);

    const providers = await getProviders(c);
    const cache = new CacheManager(c.env.PICKSLOT_EMAIL_CACHE);
    const emailService = new EmailService(cache, defaultConfig);

    const statuses = await emailService.getProviderStatuses(providers);

    return c.json({ providers: statuses });
  } catch (error) {
    console.error("Get provider status error:", error);
    return c.json(
      {
        error: error instanceof Error ? error.message : "Unknown error",
      },
      500
    );
  }
});

app.post("/providers/test", async (c) => {
  try {
    const providers = await getProviders(c);
    const cache = new CacheManager(c.env.PICKSLOT_EMAIL_CACHE);
    const emailService = new EmailService(cache, defaultConfig);

    const results = await emailService.testProviders(providers);

    return c.json({ results });
  } catch (error) {
    console.error("Test providers error:", error);
    return c.json(
      {
        error: error instanceof Error ? error.message : "Unknown error",
      },
      500
    );
  }
});

app.put("/providers/config", async (c) => {
  try {
    const { providers }: { providers: EmailProviderConfig[] } =
      await c.req.json();

    // Validate providers
    for (const provider of providers) {
      if (!provider.id || !provider.type || !provider.config?.apiKey) {
        return c.json({ error: "Invalid provider configuration" }, 400);
      }
    }

    const cache = new CacheManager(c.env.PICKSLOT_EMAIL_CACHE);
    await cache.setConfig("providers", providers);

    return c.json({ message: "Provider configuration updated successfully" });
  } catch (error) {
    console.error("Update config error:", error);
    return c.json(
      {
        error: error instanceof Error ? error.message : "Unknown error",
      },
      500
    );
  }
});

export default app;
