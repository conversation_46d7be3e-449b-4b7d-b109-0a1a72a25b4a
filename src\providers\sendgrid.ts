import { BaseEmailProvider } from './base.js';
import { EmailData, EmailResponse } from '../types/email.js';

export class SendGridProvider extends BaseEmailProvider {
  async send(emailData: EmailData): Promise<EmailResponse> {
    this.validateEmailData(emailData);

    const payload = {
      personalizations: [{
        to: this.normalizeEmailArray(emailData.to).map(email => ({ email })),
        cc: emailData.cc?.map(email => ({ email })),
        bcc: emailData.bcc?.map(email => ({ email })),
        subject: emailData.subject
      }],
      from: { email: this.getFromEmail(emailData) },
      content: [
        ...(emailData.text ? [{ type: 'text/plain', value: emailData.text }] : []),
        ...(emailData.html ? [{ type: 'text/html', value: emailData.html }] : [])
      ],
      attachments: emailData.attachments?.map(att => ({
        content: att.content,
        filename: att.filename,
        type: att.contentType || 'application/octet-stream',
        disposition: 'attachment'
      }))
    };

    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`SendGrid API error: ${error || response.statusText}`);
    }

    const messageId = response.headers.get('X-Message-Id') || 'unknown';

    return {
      success: true,
      messageId,
      provider: this.config.id
    };
  }
}