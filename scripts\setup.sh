#!/bin/bash

echo "Setting up PickSlot Email Service..."

# Create KV namespaces
echo "Creating KV namespaces..."
wrangler kv:namespace create "PICKSLOT_EMAIL_CACHE"
wrangler kv:namespace create "PICKSLOT_EMAIL_CACHE" --preview

echo "Please update wrangler.jsonc with the KV namespace IDs shown above."
echo ""
echo "Next steps:"
echo "1. Update wrangler.jsonc with your KV namespace IDs"
echo "2. Set your API keys as secrets:"
echo "   wrangler secret put RESEND_API_KEY"
echo "   wrangler secret put BREVO_API_KEY"
echo "   wrangler secret put MAILGUN_API_KEY"
echo "   wrangler secret put SENDGRID_API_KEY"
echo "   wrangler secret put POSTMARK_API_KEY"
echo "   wrangler secret put MAILGUN_DOMAIN"
echo "3. Run 'pnpm run deploy' to deploy the service"