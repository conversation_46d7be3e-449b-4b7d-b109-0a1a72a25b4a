import { BaseEmailProvider } from './base.js';
import { EmailData, EmailResponse } from '../types/email.js';

export class BrevoProvider extends BaseEmailProvider {
  async send(emailData: EmailData): Promise<EmailResponse> {
    this.validateEmailData(emailData);

    const payload = {
      sender: { email: this.getFromEmail(emailData) },
      to: this.normalizeEmailArray(emailData.to).map(email => ({ email })),
      subject: emailData.subject,
      htmlContent: emailData.html,
      textContent: emailData.text,
      cc: emailData.cc?.map(email => ({ email })),
      bcc: emailData.bcc?.map(email => ({ email })),
      attachment: emailData.attachments?.map(att => ({
        name: att.filename,
        content: att.content
      }))
    };

    const response = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'api-key': this.config.config.apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Brevo API error: ${result.message || response.statusText}`);
    }

    return {
      success: true,
      messageId: result.messageId,
      provider: this.config.id
    };
  }
}