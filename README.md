# PickSlot Email Service

A dynamic multi-provider email system built with Cloudflare Workers, TypeScript, and Hono framework.

## Features

- **Multi-Provider Support**: Resend, Brevo, Mailgun, SendGrid, Postmark
- **Intelligent Load Balancing**: Priority-based routing with round-robin and weight-based distribution
- **Circuit Breaker Pattern**: Automatic failover and provider health monitoring
- **Dynamic Configuration**: Runtime provider configuration updates
- **Bulk Email Support**: Send multiple emails efficiently
- **Comprehensive Error Handling**: Detailed error reporting and retry logic

## Quick Start

### 1. Setup KV Namespace

```bash
# Create KV namespace
wrangler kv:namespace create "PICKSLOT_EMAIL_CACHE"
wrangler kv:namespace create "PICKSLOT_EMAIL_CACHE" --preview

# Update wrangler.jsonc with the returned IDs
```

### 2. Configure Environment Variables

```bash
# Set your API keys as secrets
wrangler secret put RESEND_API_KEY
wrangler secret put BREVO_API_KEY
wrangler secret put MAILGUN_API_KEY
wrangler secret put SENDGRID_API_KEY
wrangler secret put POSTM<PERSON>K_API_KEY

# For Mailgun, also set domain
wrangler secret put MA<PERSON><PERSON><PERSON>_DOMAIN
```

### 3. Install and Deploy

```bash
pnpm install
pnpm run deploy
```

## API Endpoints

### Send Single Email

```bash
POST /send
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "Hello World",
  "html": "<h1>Hello!</h1>",
  "text": "Hello!",
  "from": "<EMAIL>"
}
```

### Send Bulk Emails

```bash
POST /send/bulk
Content-Type: application/json

{
  "emails": [
    {
      "to": "<EMAIL>",
      "subject": "Hello User 1",
      "html": "<h1>Hello User 1!</h1>"
    },
    {
      "to": "<EMAIL>",
      "subject": "Hello User 2",
      "html": "<h1>Hello User 2!</h1>"
    }
  ]
}
```

### Get Provider Status

```bash
GET /providers/status
```

### Test All Providers

```bash
POST /providers/test
```

### Update Provider Configuration

```bash
PUT /providers/config
Content-Type: application/json

{
  "providers": [
    {
      "id": "resend-primary",
      "type": "resend",
      "priority": 1,
      "weight": 3,
      "enabled": true,
      "config": {
        "apiKey": "your-resend-key",
        "fromEmail": "<EMAIL>"
      }
    }
  ]
}
```

## Configuration

### Provider Configuration Schema

```typescript
interface EmailProviderConfig {
  id: string; // Unique identifier
  type: "resend" | "brevo" | "mailgun" | "sendgrid" | "postmark";
  priority: 1 | 2 | 3; // 1 = highest priority
  weight?: number; // For load balancing within same priority
  enabled: boolean;
  config: {
    apiKey: string;
    domain?: string; // Required for Mailgun
    fromEmail?: string; // Default sender email
    [key: string]: any;
  };
}
```

### Load Balancing Strategy

1. **Priority-based**: Try priority 1 providers first, then 2, then 3
2. **Round-robin**: Distribute requests evenly within same priority
3. **Weight-based**: If weights specified, distribute according to ratios
4. **Circuit breaker**: Temporarily disable failing providers

### Circuit Breaker Configuration

```typescript
{
  failureThreshold: 5,      // Failures before circuit opens
  resetTimeout: 60000,      // Time before retry (ms)
  monitoringPeriod: 300000  // Monitoring window (ms)
}
```

## Development

```bash
# Start development server
pnpm run dev

# Generate types
pnpm run cf-typegen

# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage
```

## Monitoring

The service provides comprehensive monitoring through:

- Provider health status tracking
- Failure count and circuit breaker status
- Request/response logging
- Performance metrics

## Security

- API keys stored as Cloudflare secrets
- Input validation and sanitization
- CORS configuration
- Rate limiting (implement as needed)

## Error Handling

The service implements robust error handling:

- Automatic failover between providers
- Exponential backoff for failed providers
- Detailed error reporting
- Circuit breaker pattern for reliability

## License

MIT License
