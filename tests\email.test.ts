import { describe, it, expect, vi, beforeEach } from "vitest";
import { EmailService } from "../src/services/email.js";
import { CacheManager } from "../src/utils/cache.js";
import { EmailProviderConfig, EmailData } from "../src/types/email.js";

// Mock KV namespace
const mockKV = {
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  list: vi.fn(),
};

describe("EmailService", () => {
  let emailService: EmailService;
  let cacheManager: CacheManager;

  const mockProviders: EmailProviderConfig[] = [
    {
      id: "test-resend",
      type: "resend",
      priority: 1,
      enabled: true,
      config: {
        apiKey: "test-key",
        fromEmail: "<EMAIL>",
      },
    },
  ];

  const mockEmailData: EmailData = {
    to: "<EMAIL>",
    subject: "Test Email",
    html: "<h1>Test</h1>",
    text: "Test",
  };

  beforeEach(() => {
    vi.clearAllMocks();
    cacheManager = new CacheManager(mockKV as any);
    emailService = new EmailService(cacheManager, {
      maxRetries: 3,
      retryDelay: 100,
      circuitBreaker: {
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 300000,
      },
    });
  });

  it("should send email successfully", async () => {
    // Mock successful API response
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ id: "test-message-id" }),
    });

    mockKV.get.mockResolvedValue(null); // No cached status

    const result = await emailService.send(mockEmailData, mockProviders);

    expect(result.success).toBe(true);
    expect(result.messageId).toBe("test-message-id");
    expect(result.provider).toBe("test-resend");
  });

  it("should handle provider failure and retry", async () => {
    // Mock failed then successful API response
    global.fetch = vi
      .fn()
      .mockResolvedValueOnce({
        ok: false,
        statusText: "Server Error",
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ id: "test-message-id" }),
      });

    mockKV.get.mockResolvedValue(null);

    const multipleProviders = [
      ...mockProviders,
      {
        id: "test-brevo",
        type: "brevo" as const,
        priority: 2,
        enabled: true,
        config: {
          apiKey: "test-key-2",
        },
      },
    ];

    const result = await emailService.send(mockEmailData, multipleProviders);

    expect(result.success).toBe(true);
    expect(result.failedProviders).toContain("test-resend");
  });

  it("should validate email data", async () => {
    const invalidEmailData = {
      to: "",
      subject: "",
      html: "",
    };

    await expect(
      emailService.send(invalidEmailData as EmailData, mockProviders)
    ).rejects.toThrow();
  });
});
