#!/bin/bash

echo "🚀 Triển khai PickSlot Email Service..."

# Kiểm tra xem đã có KV namespace chưa
if ! grep -q "kv_namespaces" wrangler.jsonc; then
    echo "❌ KV namespace chưa được thiết lập. Chạy ./scripts/create-kv.sh trước."
    exit 1
fi

# Generate types
echo "📝 Generating TypeScript types..."
pnpm run cf-typegen

# Run tests
echo "🧪 Chạy tests..."
pnpm test

if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Deployment aborted."
    exit 1
fi

# Deploy to Cloudflare Workers
echo "🌐 Deploying to Cloudflare Workers..."
pnpm run deploy

if [ $? -eq 0 ]; then
    echo "✅ Deployment thành công!"
    echo "🔗 Service URL sẽ được hiển thị ở trên"
    echo ""
    echo "📋 Các endpoint có sẵn:"
    echo "  GET  / - Service info"
    echo "  POST /send - Gửi email đơn"
    echo "  POST /send/bulk - Gửi email hàng loạt"
    echo "  GET  /providers/status - Trạng thái providers"
    echo "  POST /providers/test - Test providers"
    echo "  PUT  /providers/config - Cập nhật cấu hình"
else
    echo "❌ Deployment failed!"
    exit 1
fi