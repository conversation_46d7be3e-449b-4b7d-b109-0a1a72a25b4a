#!/bin/bash

echo "🔐 Thiết lập API Keys cho Email Providers..."

echo "Nhập API keys cho các email providers (để trống nếu không sử dụng):"

# Resend
read -p "Resend API Key: " RESEND_KEY
if [ ! -z "$RESEND_KEY" ]; then
    echo $RESEND_KEY | wrangler secret put RESEND_API_KEY
    echo "✅ Resend API Key đã được thiết lập"
fi

# Brevo
read -p "Brevo API Key: " BREVO_KEY
if [ ! -z "$BREVO_KEY" ]; then
    echo $BREVO_KEY | wrangler secret put BREVO_API_KEY
    echo "✅ Brevo API Key đã được thiết lập"
fi

# Mailgun
read -p "Mailgun API Key: " MAILGUN_KEY
if [ ! -z "$MAILGUN_KEY" ]; then
    echo $MAILGUN_KEY | wrangler secret put MAILGUN_API_KEY
    read -p "Mailgun Domain: " MAILGUN_DOMAIN
    echo $MAILGUN_DOMAIN | wrangler secret put MAILGUN_DOMAIN
    echo "✅ Mailgun API Key và Domain đã được thiết lập"
fi

# SendGrid
read -p "SendGrid API Key: " SENDGRID_KEY
if [ ! -z "$SENDGRID_KEY" ]; then
    echo $SENDGRID_KEY | wrangler secret put SENDGRID_API_KEY
    echo "✅ SendGrid API Key đã được thiết lập"
fi

# Postmark
read -p "Postmark API Key: " POSTMARK_KEY
if [ ! -z "$POSTMARK_KEY" ]; then
    echo $POSTMARK_KEY | wrangler secret put POSTMARK_API_KEY
    echo "✅ Postmark API Key đã được thiết lập"
fi

echo "🎉 Tất cả API keys đã được thiết lập thành công!"