import { EmailProviderConfig } from '../types/email.js';
import { CircuitBreaker } from './circuitBreaker.js';

export class LoadBalancer {
  private roundRobinCounters: Map<number, number> = new Map();

  constructor(private circuitBreaker: CircuitBreaker) {}

  async selectProvider(providers: EmailProviderConfig[]): Promise<EmailProviderConfig | null> {
    // Filter enabled providers
    const enabledProviders = providers.filter(p => p.enabled);
    
    if (enabledProviders.length === 0) {
      return null;
    }

    // Group by priority
    const priorityGroups = new Map<number, EmailProviderConfig[]>();
    enabledProviders.forEach(provider => {
      const priority = provider.priority;
      if (!priorityGroups.has(priority)) {
        priorityGroups.set(priority, []);
      }
      priorityGroups.get(priority)!.push(provider);
    });

    // Try each priority level
    const sortedPriorities = Array.from(priorityGroups.keys()).sort();
    
    for (const priority of sortedPriorities) {
      const providersInPriority = priorityGroups.get(priority)!;
      
      // Filter by circuit breaker
      const availableProviders = [];
      for (const provider of providersInPriority) {
        if (await this.circuitBreaker.canExecute(provider.id)) {
          availableProviders.push(provider);
        }
      }

      if (availableProviders.length === 0) {
        continue;
      }

      // Select provider within priority group
      const selectedProvider = this.selectFromPriorityGroup(availableProviders, priority);
      if (selectedProvider) {
        return selectedProvider;
      }
    }

    return null;
  }

  private selectFromPriorityGroup(providers: EmailProviderConfig[], priority: number): EmailProviderConfig {
    // Check if any provider has weight specified
    const hasWeights = providers.some(p => p.weight !== undefined);
    
    if (hasWeights) {
      return this.selectByWeight(providers);
    } else {
      return this.selectRoundRobin(providers, priority);
    }
  }

  private selectByWeight(providers: EmailProviderConfig[]): EmailProviderConfig {
    const totalWeight = providers.reduce((sum, p) => sum + (p.weight || 1), 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const provider of providers) {
      currentWeight += provider.weight || 1;
      if (random <= currentWeight) {
        return provider;
      }
    }
    
    return providers[0]; // Fallback
  }

  private selectRoundRobin(providers: EmailProviderConfig[], priority: number): EmailProviderConfig {
    const currentCount = this.roundRobinCounters.get(priority) || 0;
    const selectedIndex = currentCount % providers.length;
    this.roundRobinCounters.set(priority, currentCount + 1);
    
    return providers[selectedIndex];
  }
}