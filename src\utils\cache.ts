import { KVNamespace } from "@cloudflare/workers-types";
import { ProviderStatus } from "../types/email.js";

export class CacheManager {
  constructor(private kv: KVNamespace) {}

  async getProviderStatus(providerId: string): Promise<ProviderStatus | null> {
    const data = await this.kv.get(`provider:${providerId}`);
    return data ? JSON.parse(data) : null;
  }

  async setProviderStatus(
    providerId: string,
    status: ProviderStatus
  ): Promise<void> {
    await this.kv.put(`provider:${providerId}`, JSON.stringify(status), {
      expirationTtl: 3600, // 1 hour
    });
  }

  async getConfig(key: string): Promise<any> {
    console.log("Getting config from cache:", key, this.kv);
    const data = await this.kv.get(`config:${key}`);
    return data ? JSON.parse(data) : null;
  }

  async setConfig(key: string, value: any): Promise<void> {
    await this.kv.put(`config:${key}`, JSON.stringify(value));
  }

  async incrementFailureCount(providerId: string): Promise<number> {
    const status = (await this.getProviderStatus(providerId)) || {
      id: providerId,
      enabled: true,
      healthy: true,
      failureCount: 0,
    };

    status.failureCount++;
    status.lastFailure = Date.now();
    status.healthy = false;

    await this.setProviderStatus(providerId, status);
    return status.failureCount;
  }

  async resetFailureCount(providerId: string): Promise<void> {
    const status = await this.getProviderStatus(providerId);
    if (status) {
      status.failureCount = 0;
      status.healthy = true;
      status.lastFailure = undefined;
      status.nextRetryAt = undefined;
      await this.setProviderStatus(providerId, status);
    }
  }
}
