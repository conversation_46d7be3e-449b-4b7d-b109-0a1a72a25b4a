import { EmailProviderConfig } from '../types/email.js';
import { BaseEmailProvider } from '../providers/base.js';
import { ResendProvider } from '../providers/resend.js';
import { BrevoProvider } from '../providers/brevo.js';
import { MailgunProvider } from '../providers/mailgun.js';
import { SendGridProvider } from '../providers/sendgrid.js';
import { PostmarkProvider } from '../providers/postmark.js';

export class EmailProviderFactory {
  static create(config: EmailProviderConfig): BaseEmailProvider {
    switch (config.type) {
      case 'resend':
        return new ResendProvider(config);
      case 'brevo':
        return new BrevoProvider(config);
      case 'mailgun':
        return new MailgunProvider(config);
      case 'sendgrid':
        return new SendGridProvider(config);
      case 'postmark':
        return new PostmarkProvider(config);
      default:
        throw new Error(`Unsupported email provider type: ${config.type}`);
    }
  }
}