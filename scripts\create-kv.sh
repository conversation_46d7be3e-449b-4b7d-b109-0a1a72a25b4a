#!/bin/bash

echo "🚀 Tạo KV Namespace cho Email Service..."

# Tạo KV namespace cho production
echo "Tạo KV namespace cho production..."
PROD_KV=$(wrangler kv:namespace create "PICKSLOT_EMAIL_CACHE" --json)
PROD_ID=$(echo $PROD_KV | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

# Tạo KV namespace cho preview
echo "Tạo KV namespace cho preview..."
PREVIEW_KV=$(wrangler kv:namespace create "PICKSLOT_EMAIL_CACHE" --preview --json)
PREVIEW_ID=$(echo $PREVIEW_KV | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

echo "✅ KV Namespaces đã được tạo:"
echo "Production ID: $PROD_ID"
echo "Preview ID: $PREVIEW_ID"

# Cập nhật wrangler.jsonc
cat > wrangler.jsonc << EOF
{
  "\$schema": "node_modules/wrangler/config-schema.json",
  "name": "pickslot-email-service",
  "main": "src/index.ts",
  "compatibility_date": "2025-07-19",
  "kv_namespaces": [
    {
      "binding": "PICKSLOT_EMAIL_CACHE",
      "id": "$PROD_ID",
      "preview_id": "$PREVIEW_ID"
    }
  ],
  "vars": {
    "ENVIRONMENT": "production"
  }
}
EOF

echo "✅ wrangler.jsonc đã được cập nhật với KV namespace IDs"