import {
  EmailData,
  EmailResponse,
  EmailProviderConfig,
} from "../types/email.js";
import { LoadBalancerConfig } from "../types/config.js";
import { EmailProviderFactory } from "../utils/factory.js";
import { CacheManager } from "../utils/cache.js";
import { LoadBalancer } from "./loadBalancer.js";
import { CircuitBreaker } from "./circuitBreaker.js";

export class EmailService {
  private loadBalancer: LoadBalancer;
  private circuitBreaker: CircuitBreaker;

  constructor(private cache: CacheManager, private config: LoadBalancerConfig) {
    this.circuitBreaker = new CircuitBreaker(cache, config.circuitBreaker);
    this.loadBalancer = new LoadBalancer(this.circuitBreaker);
  }

  async send(
    emailData: EmailData,
    providers: EmailProviderConfig[]
  ): Promise<EmailResponse> {
    const failedProviders: string[] = [];
    let retryCount = 0;

    while (retryCount < this.config.maxRetries) {
      const selectedProvider = await this.loadBalancer.selectProvider(
        providers
      );

      if (!selectedProvider) {
        throw new Error("No available email providers");
      }

      try {
        const provider = EmailProviderFactory.create(selectedProvider);
        const result = await provider.send(emailData);

        // Record success
        await this.circuitBreaker.recordSuccess(selectedProvider.id);

        return {
          ...result,
          failedProviders:
            failedProviders.length > 0 ? failedProviders : undefined,
          retryCount: retryCount > 0 ? retryCount : undefined,
        };
      } catch (error) {
        console.error(`Provider ${selectedProvider.id} failed:`, error);

        // Record failure
        await this.circuitBreaker.recordFailure(selectedProvider.id);
        failedProviders.push(selectedProvider.id);

        // Remove failed provider from current attempt
        const providerIndex = providers.findIndex(
          (p) => p.id === selectedProvider.id
        );
        if (providerIndex > -1) {
          providers[providerIndex] = {
            ...providers[providerIndex],
            enabled: false,
          };
        }

        retryCount++;

        if (retryCount < this.config.maxRetries) {
          await new Promise((resolve) =>
            setTimeout(resolve, this.config.retryDelay)
          );
        }
      }
    }

    return {
      success: false,
      error: "All email providers failed",
      failedProviders,
      retryCount,
    };
  }

  async sendBulk(
    emails: EmailData[],
    providers: EmailProviderConfig[]
  ): Promise<EmailResponse[]> {
    const results = await Promise.allSettled(
      emails.map((email) => this.send(email, [...providers]))
    );

    return results.map((result) =>
      result.status === "fulfilled"
        ? result.value
        : { success: false, error: result.reason?.message || "Unknown error" }
    );
  }

  async getProviderStatuses(providers: EmailProviderConfig[]): Promise<any[]> {
    const statuses = await Promise.all(
      providers.map(async (provider) => {
        const status = await this.cache.getProviderStatus(provider.id);
        return {
          id: provider.id,
          type: provider.type,
          enabled: provider.enabled,
          healthy: status?.healthy ?? true,
          failureCount: status?.failureCount ?? 0,
          lastFailure: status?.lastFailure,
          nextRetryAt: status?.nextRetryAt,
        };
      })
    );

    return statuses;
  }

  async testProviders(providers: EmailProviderConfig[]): Promise<any[]> {
    const testEmail: EmailData = {
      to: "<EMAIL>",
      subject: "Test Email",
      text: "This is a test email to verify provider connectivity.",
    };

    const results = await Promise.allSettled(
      providers.map(async (providerConfig) => {
        try {
          const provider = EmailProviderFactory.create(providerConfig);
          // Note: This would actually send emails. In production, you might want to use a test mode
          const result = await provider.send(testEmail);
          return { providerId: providerConfig.id, success: true, result };
        } catch (error) {
          return {
            providerId: providerConfig.id,
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
          };
        }
      })
    );

    return results.map((result) =>
      result.status === "fulfilled"
        ? result.value
        : { success: false, error: result.reason?.message || "Test failed" }
    );
  }
}
