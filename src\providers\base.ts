import { EmailData, EmailResponse, EmailProviderConfig } from '../types/email.js';

export abstract class BaseEmailProvider {
  protected config: EmailProviderConfig;

  constructor(config: EmailProviderConfig) {
    this.config = config;
  }

  abstract send(emailData: EmailData): Promise<EmailResponse>;
  
  protected validateEmailData(emailData: EmailData): void {
    if (!emailData.to || (Array.isArray(emailData.to) && emailData.to.length === 0)) {
      throw new Error('Recipient email is required');
    }
    if (!emailData.subject) {
      throw new Error('Email subject is required');
    }
    if (!emailData.html && !emailData.text) {
      throw new Error('Email content (html or text) is required');
    }
  }

  protected normalizeEmailArray(emails: string | string[]): string[] {
    return Array.isArray(emails) ? emails : [emails];
  }

  protected getFromEmail(emailData: EmailData): string {
    return emailData.from || this.config.config.fromEmail || '<EMAIL>';
  }
}