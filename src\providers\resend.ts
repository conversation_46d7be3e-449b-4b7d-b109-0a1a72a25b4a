import { BaseEmailProvider } from './base.js';
import { EmailData, EmailResponse } from '../types/email.js';

export class ResendProvider extends BaseEmailProvider {
  async send(emailData: EmailData): Promise<EmailResponse> {
    this.validateEmailData(emailData);

    const payload = {
      from: this.getFromEmail(emailData),
      to: this.normalizeEmailArray(emailData.to),
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text,
      cc: emailData.cc,
      bcc: emailData.bcc,
      attachments: emailData.attachments?.map(att => ({
        filename: att.filename,
        content: att.content,
        content_type: att.contentType
      }))
    };

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Resend API error: ${result.message || response.statusText}`);
    }

    return {
      success: true,
      messageId: result.id,
      provider: this.config.id
    };
  }
}