import { BaseEmailProvider } from './base.js';
import { EmailData, EmailResponse } from '../types/email.js';

export class MailgunProvider extends BaseEmailProvider {
  async send(emailData: EmailData): Promise<EmailResponse> {
    this.validateEmailData(emailData);

    const formData = new FormData();
    formData.append('from', this.getFromEmail(emailData));
    formData.append('to', this.normalizeEmailArray(emailData.to).join(','));
    formData.append('subject', emailData.subject);
    
    if (emailData.html) formData.append('html', emailData.html);
    if (emailData.text) formData.append('text', emailData.text);
    if (emailData.cc) formData.append('cc', emailData.cc.join(','));
    if (emailData.bcc) formData.append('bcc', emailData.bcc.join(','));

    emailData.attachments?.forEach(att => {
      const blob = new Blob([Uint8Array.from(atob(att.content), c => c.charCodeAt(0))], {
        type: att.contentType || 'application/octet-stream'
      });
      formData.append('attachment', blob, att.filename);
    });

    const domain = this.config.config.domain;
    if (!domain) {
      throw new Error('Mailgun domain is required');
    }

    const response = await fetch(`https://api.mailgun.net/v3/${domain}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`api:${this.config.config.apiKey}`)}`
      },
      body: formData
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Mailgun API error: ${result.message || response.statusText}`);
    }

    return {
      success: true,
      messageId: result.id,
      provider: this.config.id
    };
  }
}